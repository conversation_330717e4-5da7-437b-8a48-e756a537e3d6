import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card as AntCard,
  Typography,
  Space,
  Select,
  Spin,
  message,
  Avatar,
  List,
  Tag,
} from 'antd';
import {
  DollarOutlined,
  ShoppingCartOutlined,
  TrophyOutlined,
  TeamOutlined,
  ReloadOutlined,
  CalendarOutlined,
} from '@ant-design/icons';
import { useFetchClient } from '@strapi/helper-plugin';
import {
  PageContainer,
  Card,
  CardContent,
  PageHeader,
  StatsGrid,
  StatsCard,
  Button,
} from '../components/shared';
import { DollarSign, ShoppingCart, Award, Users, TrendingUp, BarChart3 } from 'lucide-react';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
  AreaChart,
  Area,
  Cell,
} from 'recharts';

const { Title, Text } = Typography;
const { Option } = Select;

const AffiliateOverview = () => {
  const { get } = useFetchClient();

  const [loading, setLoading] = useState(false);
  const [timeRange, setTimeRange] = useState('month');
  const [stats, setStats] = useState(null);
  const [revenueData, setRevenueData] = useState([]);
  const [topAffiliates, setTopAffiliates] = useState([]);
  const [commissionStatusData, setCommissionStatusData] = useState([]);

  // Stats data for display
  const statsData = [
    {
      title: 'Tổng doanh thu từ đại lý',
      value: stats?.totalRevenue
        ? new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(
            stats.totalRevenue
          )
        : '0 ₫',
      icon: DollarSign,
      color: '#3b82f6',
    },
    {
      title: 'Tổng đơn hàng từ đại lý',
      value: stats?.totalOrders || 0,
      icon: ShoppingCart,
      color: '#10b981',
    },
    {
      title: 'Hoa hồng đã chi',
      value: stats?.paidCommissions
        ? new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(
            stats.paidCommissions
          )
        : '0 ₫',
      icon: Award,
      color: '#059669',
    },
    {
      title: 'Hoa hồng chưa chi',
      value: stats?.unpaidCommissions
        ? new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(
            stats.unpaidCommissions
          )
        : '0 ₫',
      icon: Users,
      color: '#f59e0b',
    },
  ];

  // Sample data for charts (used as fallback when API data is not available)
  const sampleRevenueData = [
    { month: 'T1', revenue: 45000000, orders: 120, commissions: 2250000 },
    { month: 'T2', revenue: 52000000, orders: 140, commissions: 2600000 },
    { month: 'T3', revenue: 48000000, orders: 130, commissions: 2400000 },
    { month: 'T4', revenue: 61000000, orders: 165, commissions: 3050000 },
    { month: 'T5', revenue: 55000000, orders: 150, commissions: 2750000 },
    { month: 'T6', revenue: 67000000, orders: 180, commissions: 3350000 },
  ];

  const sampleTopAffiliates = [
    {
      id: 1,
      name: 'Nguyễn Thị Mai',
      phone: '0901234567',
      email: '<EMAIL>',
      avatar: '',
      totalRevenue: 25000000,
      totalOrders: 85,
      totalCommissions: 1250000,
      rank: 1,
    },
    {
      id: 2,
      name: 'Trần Văn Minh',
      phone: '0912345678',
      email: '<EMAIL>',
      avatar: '',
      totalRevenue: 22000000,
      totalOrders: 78,
      totalCommissions: 1100000,
      rank: 2,
    },
    {
      id: 3,
      name: 'Lê Thị Hương',
      phone: '0923456789',
      email: '<EMAIL>',
      avatar: '',
      totalRevenue: 20000000,
      totalOrders: 72,
      totalCommissions: 1000000,
      rank: 3,
    },
    {
      id: 4,
      name: 'Phạm Quốc Anh',
      phone: '0934567890',
      email: '<EMAIL>',
      avatar: '',
      totalRevenue: 18000000,
      totalOrders: 65,
      totalCommissions: 900000,
      rank: 4,
    },
    {
      id: 5,
      name: 'Hoàng Thị Lan',
      phone: '0945678901',
      email: '<EMAIL>',
      avatar: '',
      totalRevenue: 16000000,
      totalOrders: 58,
      totalCommissions: 800000,
      rank: 5,
    },
  ];
