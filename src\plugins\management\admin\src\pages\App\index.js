import React, { useState } from 'react';
import { Switch, Route } from 'react-router-dom';
import Sidebar from '../../components/Sidebar';
import DashboardHeader from '../../components/DashboardHeader';
import ParamBasedNavigation from '../../components/ParamBasedNavigation';
import pluginId from '../../pluginId';

// Import Be Vietnam Pro font
import '@fontsource/be-vietnam-pro/100.css';
import '@fontsource/be-vietnam-pro/200.css';
import '@fontsource/be-vietnam-pro/300.css';
import '@fontsource/be-vietnam-pro/400.css';
import '@fontsource/be-vietnam-pro/500.css';
import '@fontsource/be-vietnam-pro/600.css';
import '@fontsource/be-vietnam-pro/700.css';
import '@fontsource/be-vietnam-pro/800.css';
import '@fontsource/be-vietnam-pro/900.css';

// Import global styles
import '../../styles/global.css';

// Import components
import ProductManagement from '../../components/ProductManagement';
import OrderManagement from '../../components/OrderManagement';
import CategoryManagement from '../../components/CategoryManagement';
import BrandManagement from '../../components/BrandManagement';
import NewsCategories from '../../components/NewsCategories';
import NewsManagement from '../../components/NewsManagement';

// Import pages
import WithdrawalList from '../../components/WithdrawalList';
import AffiliateOverview from '../../components/AffiliateOverview';
import PromotionManagement from '../../components/PromotionManagement';
import UserList from '../../components/UserList';
import Settings from '../../components/Settings';
import CommissionList from '../../components/CommissionList';
import Dashboard from '../Dashboard';

// TODO: Create detail components later
// import OrderDetail from '../../components/OrderDetail';
// import UserDetail from '../../components/UserDetail';

// Simple NotFound component
const NotFound = () => {
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '50vh',
        fontFamily: 'Be Vietnam Pro, sans-serif',
      }}
    >
      <h1 style={{ fontSize: '48px', color: '#6b7280', margin: '0 0 16px 0' }}>
        404
      </h1>
      <p style={{ fontSize: '18px', color: '#9ca3af', margin: 0 }}>
        Trang không tìm thấy
      </p>
    </div>
  );
};

// Helper function to create management container
const ManagementContainer = ({ children, ...restProps }) => {
  return (
    <div className="management-container" {...restProps}>
      {children}
    </div>
  );
};

const App = () => {
  // Lấy trạng thái collapsed từ localStorage, mặc định là false
  const [collapsed, setCollapsed] = useState(() => {
    const saved = localStorage.getItem('sidebar-collapsed');
    return saved ? JSON.parse(saved) : false;
  });

  // Lưu trạng thái collapsed vào localStorage khi thay đổi
  const toggleCollapsed = () => {
    const newCollapsed = !collapsed;
    setCollapsed(newCollapsed);
    localStorage.setItem('sidebar-collapsed', JSON.stringify(newCollapsed));
  };

  return (
    <ManagementContainer className="management-plugin">
      <div style={{ display: 'flex', minHeight: '100vh' }}>
        <Sidebar collapsed={collapsed} />

        {/* Toggle Button */}
        <button
          onClick={toggleCollapsed}
          style={{
            position: 'fixed',
            top: '20px',
            left: collapsed ? '80px' : '265px',
            zIndex: 1000,
            background: '#ffffff',
            border: '1px solid #e2e8f0',
            borderRadius: '8px',
            padding: '8px',
            cursor: 'pointer',
            transition:
              'left 0.4s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.3s ease, transform 0.2s ease',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: '32px',
            height: '32px',
          }}
        >
          <span
            style={{
              fontSize: '12px',
              color: '#6b7280',
              transition: 'transform 0.3s ease',
              transform: collapsed ? 'rotate(0deg)' : 'rotate(180deg)',
            }}
          >
            →
          </span>
        </button>

        <div
          style={{
            marginLeft: 0,
            transition: 'margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
            flex: 1,
            minHeight: '100vh',
            background: '#f8fafc',
          }}
        >
          <DashboardHeader collapsed={collapsed} />

          <div
            style={{
              paddingTop: '80px', // Space for fixed header
            }}
          >
            <Switch>
              <Route
                path={`/plugins/${pluginId}`}
                component={Dashboard}
                exact
              />
              <Route
                path={`/plugins/${pluginId}/Dashboard`}
                component={Dashboard}
                exact
              />
              <Route
                path={`/plugins/${pluginId}/orders`}
                component={OrderManagement}
                exact
              />
              <Route
                path={`/plugins/${pluginId}/news/categories`}
                component={NewsCategories}
                exact
              />
              <Route
                path={`/plugins/${pluginId}/news/articles`}
                component={NewsManagement}
                exact
              />
              <Route
                path={`/plugins/${pluginId}/products/categories`}
                component={CategoryManagement}
                exact
              />
              <Route
                path={`/plugins/${pluginId}/products/brands`}
                component={BrandManagement}
                exact
              />
              <Route
                path={`/plugins/${pluginId}/products/list`}
                component={ProductManagement}
                exact
              />
              <Route
                path={`/plugins/${pluginId}/products/promotions`}
                component={PromotionManagement}
                exact
              />
              <Route
                path={`/plugins/${pluginId}/users`}
                component={UserList}
                exact
              />
              <Route
                path={`/plugins/${pluginId}/commissions`}
                component={CommissionList}
                exact
              />
              {/* <Route exact path="/" component={Dashboard} />
              <Route path="/dashboard" component={Dashboard} />
              <Route path="/orders" component={OrderManagement} />
              <Route path="/users" component={UserList} />
              <Route path="/commissions" component={CommissionList} />
              <Route path="/affiliate/withdrawals" component={WithdrawalList} />
              <Route path="/affiliate/overview" component={AffiliateOverview} />
              <Route path="/news/categories" component={NewsCategories} />
              <Route path="/news/articles" component={NewsManagement} />
              <Route
                path="/products/categories"
                component={CategoryManagement}
              />
              <Route path="/products/brands" component={BrandManagement} />
              <Route path="/products/list" component={ProductManagement} />
              <Route
                path="/products/promotions"
                component={PromotionManagement}
              />
              <Route path="/settings" component={Settings} />
              <Route path="/param-demo" component={ParamBasedNavigation} />
              <Route component={NotFound} /> */}
            </Switch>
          </div>
        </div>
      </div>
    </ManagementContainer>
  );
};

export default App;
